import logging
import re
from typing import Any


__all__ = ['TextBoundaryProcessor']


logger = logging.getLogger(__name__)


class TextBoundaryProcessor:
    """
    Processes text selections to ensure proper word boundaries and spacing.

    This class addresses the text rewriting bug where partial word selections
    lead to merged words and missing spaces in the final output.
    """

    # Regex patterns for word boundary detection
    WORD_CHAR_PATTERN = re.compile(r'\w')
    WORD_BOUNDARY_PATTERN = re.compile(r'\b')
    PUNCTUATION_PATTERN = re.compile(r'[,.!?;:]')
    WHITESPACE_PATTERN = re.compile(r'\s')
    HYPHENATED_WORD_PATTERN = re.compile(r'\w+-\w+')

    def analyze_selection_boundaries(self, full_text: str, snippet: str, snippet_index: int) -> dict[str, Any]:
        """
        Analyze text selection boundaries to detect mid-word selections.

        Args:
            full_text: The complete text containing the snippet
            snippet: The selected text snippet
            snippet_index: The starting index of the snippet in full_text

        Returns:
            Dictionary containing boundary analysis results
        """
        if snippet_index == -1:
            return {
                'is_valid_selection': False,
                'starts_mid_word': False,
                'ends_mid_word': False,
                'needs_expansion': False,
                'expanded_snippet': snippet,
                'expanded_start_index': 0,
                'reason': 'Snippet not found in full text',
            }

        snippet_end_index = snippet_index + len(snippet)

        # Check if selection starts mid-word
        starts_mid_word = self._starts_mid_word(full_text, snippet_index)

        # Check if selection ends mid-word
        ends_mid_word = self._ends_mid_word(full_text, snippet_end_index)

        # Determine if expansion is needed
        needs_expansion = starts_mid_word or ends_mid_word

        # Calculate expanded boundaries if needed
        expanded_snippet = snippet
        expanded_start_index = snippet_index

        if needs_expansion:
            expanded_start_index, expanded_end_index = self._calculate_expanded_boundaries(
                full_text, snippet_index, snippet_end_index
            )
            expanded_snippet = full_text[expanded_start_index:expanded_end_index]

        return {
            'is_valid_selection': True,
            'starts_mid_word': starts_mid_word,
            'ends_mid_word': ends_mid_word,
            'needs_expansion': needs_expansion,
            'expanded_snippet': expanded_snippet,
            'expanded_start_index': expanded_start_index,
            'original_snippet': snippet,
            'original_start_index': snippet_index,
            'reason': self._get_analysis_reason(starts_mid_word, ends_mid_word),
        }

    def process_text_for_editing(
        self, full_text: str, snippet: str, result_length: int | None = None
    ) -> dict[str, Any]:
        """
        Process text selection for editing operations with boundary analysis.

        Args:
            full_text: The complete text containing the snippet
            snippet: The selected text snippet to be edited
            result_length: Optional maximum length constraint

        Returns:
            Dictionary containing processed text data for AI editing
        """
        snippet_index = full_text.find(snippet)

        if snippet_index == -1:
            logger.warning('Snippet not found in full_text; returning original text unchanged.')
            return {
                'full_text': full_text,
                'snippet': snippet,
                'text_before_snippet': '',
                'text_after_snippet': '',
                'result_length': result_length,
                'boundary_analysis': {
                    'is_valid_selection': False,
                    'needs_expansion': False,
                    'reason': 'Snippet not found in full text',
                },
            }

        # Analyze boundaries
        boundary_analysis = self.analyze_selection_boundaries(full_text, snippet, snippet_index)

        # Use expanded snippet if boundary expansion is needed
        if boundary_analysis['needs_expansion']:
            processed_snippet = boundary_analysis['expanded_snippet']
            processed_start_index = boundary_analysis['expanded_start_index']

            logger.info(f"Expanded snippet from '{snippet}' to '{processed_snippet}' to maintain word boundaries")
        else:
            processed_snippet = snippet
            processed_start_index = snippet_index

        # Calculate text boundaries
        text_before_snippet = full_text[:processed_start_index]
        text_after_snippet = full_text[processed_start_index + len(processed_snippet) :]

        return {
            'full_text': full_text,
            'snippet': processed_snippet,
            'text_before_snippet': text_before_snippet,
            'text_after_snippet': text_after_snippet,
            'result_length': result_length,
            'boundary_analysis': boundary_analysis,
        }

    def validate_reconstructed_text(self, text_before: str, new_snippet: str, text_after: str) -> tuple[str, bool]:
        """
        Validate and fix spacing issues in reconstructed text.

        Args:
            text_before: Text before the snippet
            new_snippet: The new/rewritten snippet
            text_after: Text after the snippet

        Returns:
            Tuple of (corrected_text, was_corrected)
        """
        reconstructed = text_before + new_snippet + text_after
        original_reconstructed = reconstructed

        # Check for common spacing issues and fix them
        corrected_text = self._fix_spacing_issues(reconstructed)

        was_corrected = corrected_text != original_reconstructed

        if was_corrected:
            logger.info('Fixed spacing issues in reconstructed text')

        return corrected_text, was_corrected

    def _starts_mid_word(self, text: str, index: int) -> bool:
        """Check if the given index starts in the middle of a word."""
        if index == 0:
            return False

        # Check if the character before the index is a word character
        # and the character at the index is also a word character
        char_before = text[index - 1] if index > 0 else ''
        char_at = text[index] if index < len(text) else ''

        return bool(self.WORD_CHAR_PATTERN.match(char_before)) and bool(self.WORD_CHAR_PATTERN.match(char_at))

    def _ends_mid_word(self, text: str, index: int) -> bool:
        """Check if the given index ends in the middle of a word."""
        if index >= len(text):
            return False

        # Check if the character at the index is a word character
        # and the character before the index is also a word character
        char_at = text[index] if index < len(text) else ''
        char_before = text[index - 1] if index > 0 else ''

        return bool(self.WORD_CHAR_PATTERN.match(char_at)) and bool(self.WORD_CHAR_PATTERN.match(char_before))

    def _calculate_expanded_boundaries(self, text: str, start_index: int, end_index: int) -> tuple[int, int]:
        """
        Calculate expanded boundaries to include complete words.

        Args:
            text: The full text
            start_index: Original start index
            end_index: Original end index

        Returns:
            Tuple of (expanded_start_index, expanded_end_index)
        """
        expanded_start = start_index
        expanded_end = end_index

        # Expand start boundary to include complete word
        while expanded_start > 0 and bool(self.WORD_CHAR_PATTERN.match(text[expanded_start - 1])):
            expanded_start -= 1

        # Expand end boundary to include complete word
        while expanded_end < len(text) and bool(self.WORD_CHAR_PATTERN.match(text[expanded_end])):
            expanded_end += 1

        return expanded_start, expanded_end

    def _get_analysis_reason(self, starts_mid_word: bool, ends_mid_word: bool) -> str:
        """Get a human-readable reason for the boundary analysis result."""
        if starts_mid_word and ends_mid_word:
            return 'Selection starts and ends mid-word'
        elif starts_mid_word:
            return 'Selection starts mid-word'
        elif ends_mid_word:
            return 'Selection ends mid-word'
        else:
            return 'Selection has proper word boundaries'

    def _fix_spacing_issues(self, text: str) -> str:
        """
        Fix common spacing issues in reconstructed text.

        Args:
            text: The text to fix

        Returns:
            Text with spacing issues corrected
        """
        # Fix missing spaces after punctuation
        text = re.sub(r'([,.!?;:])([A-Za-z])', r'\1 \2', text)

        # Fix multiple consecutive spaces
        text = re.sub(r'\s+', ' ', text)

        # Fix spaces before punctuation (except for special cases)
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)

        return text
