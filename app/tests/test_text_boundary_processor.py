import pytest

from services import TextBoundaryProcessor


class TestTextBoundaryProcessor:
    """Test cases for TextBoundaryProcessor functionality."""

    @pytest.fixture
    def processor(self):
        """Create a TextBoundaryProcessor instance."""
        return TextBoundaryProcessor()

    def test_analyze_selection_boundaries_complete_words(self, processor):
        """Test boundary analysis with complete word selection."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'organization encountered'
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is False
        assert result['expanded_snippet'] == snippet
        assert result['reason'] == 'Selection has proper word boundaries'

    def test_analyze_selection_boundaries_starts_mid_word(self, processor):
        """Test boundary analysis when selection starts mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'rganization encountered'  # Starts mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is True
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection starts mid-word'

    def test_analyze_selection_boundaries_ends_mid_word(self, processor):
        """Test boundary analysis when selection ends mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'organization encoun'  # Ends mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is True
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection ends mid-word'

    def test_analyze_selection_boundaries_both_mid_word(self, processor):
        """Test boundary analysis when selection starts and ends mid-word."""
        full_text = 'The organization encountered challenges.'
        snippet = 'rganization encoun'  # Both start and end mid-word
        snippet_index = full_text.find(snippet)

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is True
        assert result['starts_mid_word'] is True
        assert result['ends_mid_word'] is True
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization encountered'
        assert result['reason'] == 'Selection starts and ends mid-word'

    def test_analyze_selection_boundaries_snippet_not_found(self, processor):
        """Test boundary analysis when snippet is not found."""
        full_text = 'The organization encountered challenges.'
        snippet = 'nonexistent text'
        snippet_index = -1

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        assert result['is_valid_selection'] is False
        assert result['starts_mid_word'] is False
        assert result['ends_mid_word'] is False
        assert result['needs_expansion'] is False
        assert result['expanded_snippet'] == snippet
        assert result['reason'] == 'Snippet not found in full text'

    def test_process_text_for_editing_normal_case(self, processor):
        """Test text processing for normal word boundary case."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'organization encountered'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' challenges during implementation.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_process_text_for_editing_with_expansion(self, processor):
        """Test text processing when boundary expansion is needed."""
        full_text = 'The organization encountered challenges during implementation.'
        snippet = 'rganization encoun'  # Needs expansion

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == 'organization encountered'  # Expanded
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' challenges during implementation.'  # Correct expectation
        assert result['boundary_analysis']['needs_expansion'] is True

    def test_process_text_for_editing_snippet_not_found(self, processor):
        """Test text processing when snippet is not found."""
        full_text = 'The organization encountered challenges.'
        snippet = 'nonexistent text'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['full_text'] == full_text
        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == ''
        assert result['text_after_snippet'] == ''
        assert result['boundary_analysis']['is_valid_selection'] is False

    def test_validate_reconstructed_text_no_issues(self, processor):
        """Test text validation when no spacing issues exist."""
        text_before = 'The '
        new_snippet = 'company faced'
        text_after = ' challenges during implementation.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The company faced challenges during implementation.'
        assert corrected_text == expected
        assert was_corrected is False

    def test_validate_reconstructed_text_missing_space_after_punctuation(self, processor):
        """Test text validation fixes missing spaces after punctuation."""
        text_before = 'The system has flaws'
        new_snippet = ',and'
        text_after = ' needs improvement.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The system has flaws, and needs improvement.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_validate_reconstructed_text_multiple_spaces(self, processor):
        """Test text validation fixes multiple consecutive spaces."""
        text_before = 'The '
        new_snippet = 'organization   encountered'
        text_after = ' challenges.'

        corrected_text, was_corrected = processor.validate_reconstructed_text(text_before, new_snippet, text_after)

        expected = 'The organization encountered challenges.'
        assert corrected_text == expected
        assert was_corrected is True

    def test_hyphenated_word_handling(self, processor):
        """Test that hyphenated words are handled correctly."""
        full_text = 'The anti-aliasing feature improved performance significantly.'
        snippet = 'anti-aliasing feature'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The '
        assert result['text_after_snippet'] == ' improved performance significantly.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_punctuation_boundary_handling(self, processor):
        """Test handling of selections that include punctuation."""
        full_text = 'Data processing, analysis and reporting were completed.'
        snippet = 'processing, analysis'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'Data '
        assert result['text_after_snippet'] == ' and reporting were completed.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_edge_case_start_of_text(self, processor):
        """Test selection at the very start of text."""
        full_text = 'Organization encountered challenges.'
        snippet = 'Organization'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == ''
        assert result['text_after_snippet'] == ' encountered challenges.'
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_edge_case_end_of_text(self, processor):
        """Test selection at the very end of text."""
        full_text = 'The organization encountered challenges.'
        snippet = 'challenges.'

        result = processor.process_text_for_editing(full_text, snippet)

        assert result['snippet'] == snippet
        assert result['text_before_snippet'] == 'The organization encountered '
        assert result['text_after_snippet'] == ''
        assert result['boundary_analysis']['needs_expansion'] is False

    def test_single_character_selection(self, processor):
        """Test single character selections."""
        full_text = 'The organization encountered challenges.'
        snippet = 'o'  # Single character that could be mid-word
        snippet_index = full_text.find(snippet)  # First 'o' in "organization"

        result = processor.analyze_selection_boundaries(full_text, snippet, snippet_index)

        # Single character in middle of word should trigger expansion
        assert result['needs_expansion'] is True
        assert result['expanded_snippet'] == 'organization'
